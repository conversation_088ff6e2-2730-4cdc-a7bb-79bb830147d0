package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 线下专区-公会明细表-周
 *
 * @date 2025-08-08 02:38:52
 */
@Table(name = "`offline_zone_data_family_week`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneDataFamilyWeek {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 周开始日期，格式YYYY-MM-DD
     */
    @Column(name= "`start_week_date`")
    private Date startWeekDate;

    /**
     * 周结束日期，格式YYYY-MM-DD
     */
    @Column(name= "`end_week_date`")
    private Date endWeekDate;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 公会名称
     */
    @Column(name= "`family_name`")
    private String familyName;

    /**
     * 基地数
     */
    @Column(name= "`basic_cnt`")
    private Integer basicCnt;

    /**
     * 线下厅数
     */
    @Column(name= "`offline_hall_cnt`")
    private Long offlineHallCnt;

    /**
     * 线下厅数占比
     */
    @Column(name= "`offline_hall_cnt_rate`")
    private BigDecimal offlineHallCntRate;

    /**
     * 线下厅收入
     */
    @Column(name= "`offline_hall_income`")
    private BigDecimal offlineHallIncome;

    /**
     * 线下厅收入占比
     */
    @Column(name= "`offline_hall_income_rate`")
    private BigDecimal offlineHallIncomeRate;

    /**
     * 线下主播数
     */
    @Column(name= "`player_cnt`")
    private Integer playerCnt;

    /**
     * 线下主播数
     */
    @Column(name= "`offline_player_cnt`")
    private Integer offlinePlayerCnt;

    /**
     * 线下主播数占比
     */
    @Column(name= "`offline_player_cnt_rate`")
    private BigDecimal offlinePlayerCntRate;

    /**
     * 受保护主播数
     */
    @Column(name= "`protected_player_cnt`")
    private Integer protectedPlayerCnt;

    /**
     * 受保护主播数占比
     */
    @Column(name= "`protected_player_cnt_rate`")
    private BigDecimal protectedPlayerCntRate;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", startWeekDate=").append(startWeekDate);
        sb.append(", endWeekDate=").append(endWeekDate);
        sb.append(", familyId=").append(familyId);
        sb.append(", familyName=").append(familyName);
        sb.append(", basicCnt=").append(basicCnt);
        sb.append(", offlineHallCnt=").append(offlineHallCnt);
        sb.append(", offlineHallCntRate=").append(offlineHallCntRate);
        sb.append(", offlineHallIncome=").append(offlineHallIncome);
        sb.append(", offlineHallIncomeRate=").append(offlineHallIncomeRate);
        sb.append(", playerCnt=").append(playerCnt);
        sb.append(", offlinePlayerCnt=").append(offlinePlayerCnt);
        sb.append(", offlinePlayerCntRate=").append(offlinePlayerCntRate);
        sb.append(", protectedPlayerCnt=").append(protectedPlayerCnt);
        sb.append(", protectedPlayerCntRate=").append(protectedPlayerCntRate);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}