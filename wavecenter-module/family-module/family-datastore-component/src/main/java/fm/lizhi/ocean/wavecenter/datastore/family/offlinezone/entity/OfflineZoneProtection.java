package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 主播跳槽保护协议
 *
 * @date 2025-08-08 02:38:52
 */
@Table(name = "`offline_zone_protection`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneProtection {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Long appId;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 上传用户 ID
     */
    @Column(name= "`upload_user_id`")
    private Long uploadUserId;

    /**
     * 厅ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 主播ID
     */
    @Column(name= "`player_id`")
    private Long playerId;

    /**
     * 协议生效开始时间
     */
    @Column(name= "`agreement_start_time`")
    private Date agreementStartTime;

    /**
     * 协议生效结束时间
     */
    @Column(name= "`agreement_end_time`")
    private Date agreementEndTime;

    /**
     * 协议更新时间
     */
    @Column(name= "`agreement_update_time`")
    private Date agreementUpdateTime;

    /**
     * 是否盖章签字：0-否，1-是
     */
    @Column(name= "`stamp_sign`")
    private Boolean stampSign;

    /**
     * 主播同意状态：-1-未处理，0-不同意，1-同意
     */
    @Column(name= "`player_agree`")
    private Integer playerAgree;

    /**
     * 是否归档：0-否，1-是
     */
    @Column(name= "`archived`")
    private Boolean archived;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 协议链接列表（逗号分隔）
     */
    @Column(name= "`agreement_urls`")
    private String agreementUrls;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", uploadUserId=").append(uploadUserId);
        sb.append(", njId=").append(njId);
        sb.append(", playerId=").append(playerId);
        sb.append(", agreementStartTime=").append(agreementStartTime);
        sb.append(", agreementEndTime=").append(agreementEndTime);
        sb.append(", agreementUpdateTime=").append(agreementUpdateTime);
        sb.append(", stampSign=").append(stampSign);
        sb.append(", playerAgree=").append(playerAgree);
        sb.append(", archived=").append(archived);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", agreementUrls=").append(agreementUrls);
        sb.append("]");
        return sb.toString();
    }
}