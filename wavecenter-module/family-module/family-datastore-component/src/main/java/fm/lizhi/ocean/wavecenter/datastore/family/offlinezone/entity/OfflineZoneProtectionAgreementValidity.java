package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 跳槽保护上传协议时效记录
 *
 * @date 2025-08-08 02:38:52
 */
@Table(name = "`offline_zone_protection_agreement_validity`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneProtectionAgreementValidity {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Long appId;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 厅ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 主播ID
     */
    @Column(name= "`player_id`")
    private Long playerId;

    /**
     * 签约时间
     */
    @Column(name= "`begin_sign_time`")
    private Date beginSignTime;

    /**
     * 过期时间
     */
    @Column(name= "`expired_time`")
    private Date expiredTime;

    /**
     * 重置次数
     */
    @Column(name= "`resets_cnt`")
    private Integer resetsCnt;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", njId=").append(njId);
        sb.append(", playerId=").append(playerId);
        sb.append(", beginSignTime=").append(beginSignTime);
        sb.append(", expiredTime=").append(expiredTime);
        sb.append(", resetsCnt=").append(resetsCnt);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}