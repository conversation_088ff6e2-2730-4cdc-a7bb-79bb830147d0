package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.util.ArrayList;
import java.util.List;

public class OfflineZoneLearningClassWhiteListExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public OfflineZoneLearningClassWhiteListExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClassWhiteList.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLearningIdIsNull() {
            addCriterion("learning_id is null");
            return (Criteria) this;
        }

        public Criteria andLearningIdIsNotNull() {
            addCriterion("learning_id is not null");
            return (Criteria) this;
        }

        public Criteria andLearningIdEqualTo(Long value) {
            addCriterion("learning_id =", value, "learningId");
            return (Criteria) this;
        }

        public Criteria andLearningIdNotEqualTo(Long value) {
            addCriterion("learning_id <>", value, "learningId");
            return (Criteria) this;
        }

        public Criteria andLearningIdGreaterThan(Long value) {
            addCriterion("learning_id >", value, "learningId");
            return (Criteria) this;
        }

        public Criteria andLearningIdGreaterThanOrEqualTo(Long value) {
            addCriterion("learning_id >=", value, "learningId");
            return (Criteria) this;
        }

        public Criteria andLearningIdLessThan(Long value) {
            addCriterion("learning_id <", value, "learningId");
            return (Criteria) this;
        }

        public Criteria andLearningIdLessThanOrEqualTo(Long value) {
            addCriterion("learning_id <=", value, "learningId");
            return (Criteria) this;
        }

        public Criteria andLearningIdIn(List<Long> values) {
            addCriterion("learning_id in", values, "learningId");
            return (Criteria) this;
        }

        public Criteria andLearningIdNotIn(List<Long> values) {
            addCriterion("learning_id not in", values, "learningId");
            return (Criteria) this;
        }

        public Criteria andLearningIdBetween(Long value1, Long value2) {
            addCriterion("learning_id between", value1, value2, "learningId");
            return (Criteria) this;
        }

        public Criteria andLearningIdNotBetween(Long value1, Long value2) {
            addCriterion("learning_id not between", value1, value2, "learningId");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andWhiteIdIsNull() {
            addCriterion("white_id is null");
            return (Criteria) this;
        }

        public Criteria andWhiteIdIsNotNull() {
            addCriterion("white_id is not null");
            return (Criteria) this;
        }

        public Criteria andWhiteIdEqualTo(Long value) {
            addCriterion("white_id =", value, "whiteId");
            return (Criteria) this;
        }

        public Criteria andWhiteIdNotEqualTo(Long value) {
            addCriterion("white_id <>", value, "whiteId");
            return (Criteria) this;
        }

        public Criteria andWhiteIdGreaterThan(Long value) {
            addCriterion("white_id >", value, "whiteId");
            return (Criteria) this;
        }

        public Criteria andWhiteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("white_id >=", value, "whiteId");
            return (Criteria) this;
        }

        public Criteria andWhiteIdLessThan(Long value) {
            addCriterion("white_id <", value, "whiteId");
            return (Criteria) this;
        }

        public Criteria andWhiteIdLessThanOrEqualTo(Long value) {
            addCriterion("white_id <=", value, "whiteId");
            return (Criteria) this;
        }

        public Criteria andWhiteIdIn(List<Long> values) {
            addCriterion("white_id in", values, "whiteId");
            return (Criteria) this;
        }

        public Criteria andWhiteIdNotIn(List<Long> values) {
            addCriterion("white_id not in", values, "whiteId");
            return (Criteria) this;
        }

        public Criteria andWhiteIdBetween(Long value1, Long value2) {
            addCriterion("white_id between", value1, value2, "whiteId");
            return (Criteria) this;
        }

        public Criteria andWhiteIdNotBetween(Long value1, Long value2) {
            addCriterion("white_id not between", value1, value2, "whiteId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated do_not_delete_during_merge Fri Aug 08 14:38:52 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_learning_class_white_list
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}