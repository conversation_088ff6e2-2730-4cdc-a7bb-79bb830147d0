package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class OfflineZoneDataHallWeekExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public OfflineZoneDataHallWeekExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataHallWeek.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNull() {
            addCriterion("start_week_date is null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNotNull() {
            addCriterion("start_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date =", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <>", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("start_week_date >", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date >=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("start_week_date <", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date not in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date not between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNull() {
            addCriterion("end_week_date is null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNotNull() {
            addCriterion("end_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date =", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <>", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("end_week_date >", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date >=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("end_week_date <", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date not in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date not between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyNameIsNull() {
            addCriterion("family_name is null");
            return (Criteria) this;
        }

        public Criteria andFamilyNameIsNotNull() {
            addCriterion("family_name is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyNameEqualTo(String value) {
            addCriterion("family_name =", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameNotEqualTo(String value) {
            addCriterion("family_name <>", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameGreaterThan(String value) {
            addCriterion("family_name >", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameGreaterThanOrEqualTo(String value) {
            addCriterion("family_name >=", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameLessThan(String value) {
            addCriterion("family_name <", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameLessThanOrEqualTo(String value) {
            addCriterion("family_name <=", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameLike(String value) {
            addCriterion("family_name like", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameNotLike(String value) {
            addCriterion("family_name not like", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameIn(List<String> values) {
            addCriterion("family_name in", values, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameNotIn(List<String> values) {
            addCriterion("family_name not in", values, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameBetween(String value1, String value2) {
            addCriterion("family_name between", value1, value2, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameNotBetween(String value1, String value2) {
            addCriterion("family_name not between", value1, value2, "familyName");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNull() {
            addCriterion("nj_id is null");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNotNull() {
            addCriterion("nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andNjIdEqualTo(Long value) {
            addCriterion("nj_id =", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotEqualTo(Long value) {
            addCriterion("nj_id <>", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThan(Long value) {
            addCriterion("nj_id >", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("nj_id >=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThan(Long value) {
            addCriterion("nj_id <", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThanOrEqualTo(Long value) {
            addCriterion("nj_id <=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdIn(List<Long> values) {
            addCriterion("nj_id in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotIn(List<Long> values) {
            addCriterion("nj_id not in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdBetween(Long value1, Long value2) {
            addCriterion("nj_id between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotBetween(Long value1, Long value2) {
            addCriterion("nj_id not between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andNjNameIsNull() {
            addCriterion("nj_name is null");
            return (Criteria) this;
        }

        public Criteria andNjNameIsNotNull() {
            addCriterion("nj_name is not null");
            return (Criteria) this;
        }

        public Criteria andNjNameEqualTo(String value) {
            addCriterion("nj_name =", value, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameNotEqualTo(String value) {
            addCriterion("nj_name <>", value, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameGreaterThan(String value) {
            addCriterion("nj_name >", value, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameGreaterThanOrEqualTo(String value) {
            addCriterion("nj_name >=", value, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameLessThan(String value) {
            addCriterion("nj_name <", value, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameLessThanOrEqualTo(String value) {
            addCriterion("nj_name <=", value, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameLike(String value) {
            addCriterion("nj_name like", value, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameNotLike(String value) {
            addCriterion("nj_name not like", value, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameIn(List<String> values) {
            addCriterion("nj_name in", values, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameNotIn(List<String> values) {
            addCriterion("nj_name not in", values, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameBetween(String value1, String value2) {
            addCriterion("nj_name between", value1, value2, "njName");
            return (Criteria) this;
        }

        public Criteria andNjNameNotBetween(String value1, String value2) {
            addCriterion("nj_name not between", value1, value2, "njName");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeIsNull() {
            addCriterion("begin_sign_time is null");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeIsNotNull() {
            addCriterion("begin_sign_time is not null");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeEqualTo(Date value) {
            addCriterion("begin_sign_time =", value, "beginSignTime");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeNotEqualTo(Date value) {
            addCriterion("begin_sign_time <>", value, "beginSignTime");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeGreaterThan(Date value) {
            addCriterion("begin_sign_time >", value, "beginSignTime");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("begin_sign_time >=", value, "beginSignTime");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeLessThan(Date value) {
            addCriterion("begin_sign_time <", value, "beginSignTime");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeLessThanOrEqualTo(Date value) {
            addCriterion("begin_sign_time <=", value, "beginSignTime");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeIn(List<Date> values) {
            addCriterion("begin_sign_time in", values, "beginSignTime");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeNotIn(List<Date> values) {
            addCriterion("begin_sign_time not in", values, "beginSignTime");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeBetween(Date value1, Date value2) {
            addCriterion("begin_sign_time between", value1, value2, "beginSignTime");
            return (Criteria) this;
        }

        public Criteria andBeginSignTimeNotBetween(Date value1, Date value2) {
            addCriterion("begin_sign_time not between", value1, value2, "beginSignTime");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(Integer value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(Integer value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(Integer value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(Integer value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(Integer value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<Integer> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<Integer> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(Integer value1, Integer value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(Integer value1, Integer value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNull() {
            addCriterion("income is null");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNotNull() {
            addCriterion("income is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeEqualTo(BigDecimal value) {
            addCriterion("income =", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotEqualTo(BigDecimal value) {
            addCriterion("income <>", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThan(BigDecimal value) {
            addCriterion("income >", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income >=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThan(BigDecimal value) {
            addCriterion("income <", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income <=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeIn(List<BigDecimal> values) {
            addCriterion("income in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotIn(List<BigDecimal> values) {
            addCriterion("income not in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income not between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntIsNull() {
            addCriterion("offline_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntIsNotNull() {
            addCriterion("offline_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntEqualTo(Integer value) {
            addCriterion("offline_player_cnt =", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntNotEqualTo(Integer value) {
            addCriterion("offline_player_cnt <>", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntGreaterThan(Integer value) {
            addCriterion("offline_player_cnt >", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("offline_player_cnt >=", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntLessThan(Integer value) {
            addCriterion("offline_player_cnt <", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("offline_player_cnt <=", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntIn(List<Integer> values) {
            addCriterion("offline_player_cnt in", values, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntNotIn(List<Integer> values) {
            addCriterion("offline_player_cnt not in", values, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("offline_player_cnt between", value1, value2, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("offline_player_cnt not between", value1, value2, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateIsNull() {
            addCriterion("offline_player_cnt_rate is null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateIsNotNull() {
            addCriterion("offline_player_cnt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateEqualTo(BigDecimal value) {
            addCriterion("offline_player_cnt_rate =", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateNotEqualTo(BigDecimal value) {
            addCriterion("offline_player_cnt_rate <>", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateGreaterThan(BigDecimal value) {
            addCriterion("offline_player_cnt_rate >", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_player_cnt_rate >=", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateLessThan(BigDecimal value) {
            addCriterion("offline_player_cnt_rate <", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_player_cnt_rate <=", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateIn(List<BigDecimal> values) {
            addCriterion("offline_player_cnt_rate in", values, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateNotIn(List<BigDecimal> values) {
            addCriterion("offline_player_cnt_rate not in", values, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_player_cnt_rate between", value1, value2, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_player_cnt_rate not between", value1, value2, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntIsNull() {
            addCriterion("protected_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntIsNotNull() {
            addCriterion("protected_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntEqualTo(Integer value) {
            addCriterion("protected_player_cnt =", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntNotEqualTo(Integer value) {
            addCriterion("protected_player_cnt <>", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntGreaterThan(Integer value) {
            addCriterion("protected_player_cnt >", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("protected_player_cnt >=", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntLessThan(Integer value) {
            addCriterion("protected_player_cnt <", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("protected_player_cnt <=", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntIn(List<Integer> values) {
            addCriterion("protected_player_cnt in", values, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntNotIn(List<Integer> values) {
            addCriterion("protected_player_cnt not in", values, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("protected_player_cnt between", value1, value2, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("protected_player_cnt not between", value1, value2, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateIsNull() {
            addCriterion("protected_player_cnt_rate is null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateIsNotNull() {
            addCriterion("protected_player_cnt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate =", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateNotEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate <>", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateGreaterThan(BigDecimal value) {
            addCriterion("protected_player_cnt_rate >", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate >=", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateLessThan(BigDecimal value) {
            addCriterion("protected_player_cnt_rate <", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate <=", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateIn(List<BigDecimal> values) {
            addCriterion("protected_player_cnt_rate in", values, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateNotIn(List<BigDecimal> values) {
            addCriterion("protected_player_cnt_rate not in", values, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("protected_player_cnt_rate between", value1, value2, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("protected_player_cnt_rate not between", value1, value2, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeIsNull() {
            addCriterion("offline_player_income is null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeIsNotNull() {
            addCriterion("offline_player_income is not null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeEqualTo(BigDecimal value) {
            addCriterion("offline_player_income =", value, "offlinePlayerIncome");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeNotEqualTo(BigDecimal value) {
            addCriterion("offline_player_income <>", value, "offlinePlayerIncome");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeGreaterThan(BigDecimal value) {
            addCriterion("offline_player_income >", value, "offlinePlayerIncome");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_player_income >=", value, "offlinePlayerIncome");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeLessThan(BigDecimal value) {
            addCriterion("offline_player_income <", value, "offlinePlayerIncome");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_player_income <=", value, "offlinePlayerIncome");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeIn(List<BigDecimal> values) {
            addCriterion("offline_player_income in", values, "offlinePlayerIncome");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeNotIn(List<BigDecimal> values) {
            addCriterion("offline_player_income not in", values, "offlinePlayerIncome");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_player_income between", value1, value2, "offlinePlayerIncome");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_player_income not between", value1, value2, "offlinePlayerIncome");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateIsNull() {
            addCriterion("offline_player_income_rate is null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateIsNotNull() {
            addCriterion("offline_player_income_rate is not null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateEqualTo(BigDecimal value) {
            addCriterion("offline_player_income_rate =", value, "offlinePlayerIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateNotEqualTo(BigDecimal value) {
            addCriterion("offline_player_income_rate <>", value, "offlinePlayerIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateGreaterThan(BigDecimal value) {
            addCriterion("offline_player_income_rate >", value, "offlinePlayerIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_player_income_rate >=", value, "offlinePlayerIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateLessThan(BigDecimal value) {
            addCriterion("offline_player_income_rate <", value, "offlinePlayerIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_player_income_rate <=", value, "offlinePlayerIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateIn(List<BigDecimal> values) {
            addCriterion("offline_player_income_rate in", values, "offlinePlayerIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateNotIn(List<BigDecimal> values) {
            addCriterion("offline_player_income_rate not in", values, "offlinePlayerIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_player_income_rate between", value1, value2, "offlinePlayerIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerIncomeRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_player_income_rate not between", value1, value2, "offlinePlayerIncomeRate");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated do_not_delete_during_merge Fri Aug 08 14:38:52 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_data_hall_week
     *
     * @mbg.generated Fri Aug 08 14:38:52 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}