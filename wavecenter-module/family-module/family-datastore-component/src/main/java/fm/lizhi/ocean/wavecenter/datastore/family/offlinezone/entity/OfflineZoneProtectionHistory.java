package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 跳槽保护历史记录
 *
 * @date 2025-08-11 09:09:31
 */
@Table(name = "`offline_zone_protection_history`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneProtectionHistory {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 跳槽保护表ID
     */
    @Column(name= "`protected_id`")
    private Long protectedId;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Long appId;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 厅ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 主播ID
     */
    @Column(name= "`player_id`")
    private Long playerId;

    /**
     * 上传用户
     */
    @Column(name= "`upload_user_id`")
    private Long uploadUserId;

    /**
     * 协议生效开始时间
     */
    @Column(name= "`agreement_start_time`")
    private Date agreementStartTime;

    /**
     * 协议生效结束时间
     */
    @Column(name= "`agreement_end_time`")
    private Date agreementEndTime;

    /**
     * 协议更新时间
     */
    @Column(name= "`agreement_update_time`")
    private Date agreementUpdateTime;

    /**
     * 是否盖章签字：0-否，1-是
     */
    @Column(name= "`stamp_sign`")
    private Boolean stampSign;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 协议链接列表 格式: [{"url":"","md5":"","name":""}]
     */
    @Column(name= "`agreement_file_json`")
    private String agreementFileJson;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", protectedId=").append(protectedId);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", njId=").append(njId);
        sb.append(", playerId=").append(playerId);
        sb.append(", uploadUserId=").append(uploadUserId);
        sb.append(", agreementStartTime=").append(agreementStartTime);
        sb.append(", agreementEndTime=").append(agreementEndTime);
        sb.append(", agreementUpdateTime=").append(agreementUpdateTime);
        sb.append(", stampSign=").append(stampSign);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", agreementFileJson=").append(agreementFileJson);
        sb.append("]");
        return sb.toString();
    }
}