package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 线下专区主播数据
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OfflineZonePlayerDataBean {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 周开始日期，格式YYYY-MM-DD
     */
    private Date startWeekDate;

    /**
     * 周结束日期，格式YYYY-MM-DD
     */
    private Date endWeekDate;

    /**
     * 主播ID
     */
    private Long userId;

    /**
     * 主播名称
     */
    private String userName;

    /**
     * 主播信息
     */
    private UserBean userInfo;

    /**
     * 实名证件号码
     */
    private String idCardNumber;

    /**
     * 实名姓名
     */
    private String idName;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 厅主名称
     */
    private String njName;

    /**
     * 厅主信息
     */
    private UserBean njInfo;

    /**
     * 签约时间
     */
    private Date beginSignTime;

    /**
     * 主播分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 主播收入
     */
    private BigDecimal income;

    /**
     * 是否受跳槽保护
     */
    private Boolean protection;

    /**
     * 保护状态描述
     */
    private String protectionStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;
}
