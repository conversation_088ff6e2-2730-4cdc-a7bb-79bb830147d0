package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 创建保护协议请求
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestCreateProtection implements RequestAppIdAware {

    @NotNull(message = "appId不能为空")
    private Long appId;

    @NotNull(message = "familyId不能为空")
    private Long familyId;

    @NotNull(message = "njId不能为空")
    private Long njId;

    @NotNull(message = "playerId不能为空")
    private Long playerId;

    @NotNull(message = "uploadUserId不能为空")
    private Long uploadUserId;

    @NotNull(message = "agreementStartTime不能为空")
    private Date agreementStartTime;

    @NotNull(message = "agreementEndTime不能为空")
    private Date agreementEndTime;

    /**
     * 是否盖章签字：0-否，1-是
     */
    private Boolean stampSign;

    /**
     * 协议链接列表（逗号分隔）
     */
    private String agreementUrls;

    /**
     * 操作人
     */
    private String operator;

    @Override
    public Integer foundIdAppId() {
        return appId != null ? appId.intValue() : null;
    }
}
