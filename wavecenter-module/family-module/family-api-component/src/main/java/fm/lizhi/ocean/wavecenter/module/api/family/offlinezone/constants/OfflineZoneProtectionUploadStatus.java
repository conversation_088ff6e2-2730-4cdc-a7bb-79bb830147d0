package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants;

import lombok.Getter;

/**
 * 线下专区-主播保护上传状态
 * <AUTHOR>
 */

@Getter
public enum OfflineZoneProtectionUploadStatus {
    /** 未上传 */
    NOT_UPLOAD(0),

    /** 已上传 */
    UPLOADED(1),

    /** 已逾期 */
    OVERDUE(2);

    private final int status;

    OfflineZoneProtectionUploadStatus(int value) {
        this.status = value;
    }

    public static OfflineZoneProtectionUploadStatus getByStatus(int status) {
        for (OfflineZoneProtectionUploadStatus value : values()) {
            if (value.status == status) {
                return value;
            }
        }
        return null;
    }
}
