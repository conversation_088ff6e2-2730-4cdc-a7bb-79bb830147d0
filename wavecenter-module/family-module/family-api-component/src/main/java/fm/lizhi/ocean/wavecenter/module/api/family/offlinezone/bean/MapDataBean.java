package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 地图数据
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MapDataBean {

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 线下厅列表
     */
    private List<MapDataRoomBean> roomList;
}
