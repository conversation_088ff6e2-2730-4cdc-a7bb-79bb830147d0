package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestDataMonitorMapData;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestDataMonitorRoomSummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorRoomSummary;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据监控
 * <AUTHOR>
 */
public interface OfflineZoneDataMonitorService {


    /**
     * 获取家族数据监控汇总
     * @param request
     * @return
     */
    Result<ResponseDataMonitorFamilySummary> getFamilySummary(@Valid RequestDataMonitorFamilySummary request);


    /**
     * 获取线下厅数据监控汇总
     * @param request
     * @return
     */
    Result<ResponseDataMonitorRoomSummary> getRoomSummary(@Valid RequestDataMonitorRoomSummary request);


    /**
     * 获取地图数据
     * @param request
     * @return
     */
    Result<List<MapDataBean>> getMapData(@Valid RequestDataMonitorMapData request);

    /**
     * 获取家族数据监控汇总失败
     */
    int GET_FAMILY_SUMMARY_FAIL = 2480000;

    /**
     * 获取线下厅数据监控汇总失败
     */
    int GET_ROOM_SUMMARY_FAIL = 2480001;

    /**
     * 获取地图数据失败
     */
    int GET_MAP_DATA_FAIL = 2480002;
}
