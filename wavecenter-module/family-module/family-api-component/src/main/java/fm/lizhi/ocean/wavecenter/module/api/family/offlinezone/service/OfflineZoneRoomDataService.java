package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneRoomDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetRoomDataList;

import javax.validation.Valid;
import java.util.List;

/**
 * 线下专区-厅数据
 * <AUTHOR>
 */
public interface OfflineZoneRoomDataService {



    /**
     * 获取厅数据列表
     * @param request
     * @return
     */
    Result<PageBean<OfflineZoneRoomDataBean>> getRoomDataList(@Valid RequestGetRoomDataList request);


    /**
     * 获取厅数据列表失败
     */
    int GET_ROOM_DATA_LIST_FAIL = 2490001;

    /**
     * 获取厅数据列表参数错误
     */
    int GET_ROOM_DATA_LIST_PARAM_ERROR = 2490001;
}
