package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 更新保护协议请求
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestUpdateProtection {

    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 协议生效开始时间
     */
    private Date agreementStartTime;

    /**
     * 协议生效结束时间
     */
    private Date agreementEndTime;

    /**
     * 是否盖章签字：0-否，1-是
     */
    private Boolean stampSign;

    /**
     * 主播同意状态：-1-未处理，0-不同意，1-同意
     */
    private Integer playerAgree;

    /**
     * 是否归档：0-否，1-是
     */
    private Boolean archived;

    /**
     * 协议链接列表（逗号分隔）
     */
    private String agreementUrls;

    /**
     * 操作人
     */
    private String operator;
}
