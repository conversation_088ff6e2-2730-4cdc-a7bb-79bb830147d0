package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestGetRoomDataList implements RequestAppIdAware {

    @NotNull(message = "appId不能为空")
    private Integer appId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 厅分类
     * @see fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneRoomCategoryEnums
     */
    private Integer category;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 排序字段
     */
    private String orderMetrics;

    /**
     * 排序类型
     */
    private String orderType;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页大小
     */
    private Integer pageSize;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
