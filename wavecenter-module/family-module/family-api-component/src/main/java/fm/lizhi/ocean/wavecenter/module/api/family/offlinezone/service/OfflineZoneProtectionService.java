package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneProtectionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestCreateProtection;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetProtectionList;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestUpdateProtection;

import javax.validation.Valid;

/**
 * 线下专区-主播跳槽保护协议服务
 * <AUTHOR>
 */
public interface OfflineZoneProtectionService {

    /**
     * 获取保护协议列表
     * 
     * @param request 请求参数
     * @return 分页保护协议列表
     */
    Result<PageBean<OfflineZoneProtectionBean>> getProtectionList(@Valid RequestGetProtectionList request);

    /**
     * 创建保护协议
     * 
     * @param request 请求参数
     * @return 创建结果
     */
    Result<Boolean> createProtection(@Valid RequestCreateProtection request);

    /**
     * 更新保护协议
     * 
     * @param request 请求参数
     * @return 更新结果
     */
    Result<Boolean> updateProtection(@Valid RequestUpdateProtection request);

    /**
     * 主播同意保护协议
     * 
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 操作结果
     */
    Result<Boolean> agreeProtection(Long appId, Long playerId);

    /**
     * 主播拒绝保护协议
     * 
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 操作结果
     */
    Result<Boolean> rejectProtection(Long appId, Long playerId);

    /**
     * 删除保护协议
     * 
     * @param id 协议ID
     * @return 删除结果
     */
    Result<Boolean> deleteProtection(Long id);

    /**
     * 根据ID查询保护协议
     * 
     * @param id 协议ID
     * @return 保护协议
     */
    Result<OfflineZoneProtectionBean> getProtectionById(Long id);

    /**
     * 检查主播是否受保护
     * 
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 是否受保护
     */
    Result<Boolean> isPlayerProtected(Long appId, Long playerId);

    /**
     * 获取主播保护状态
     * 
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 保护状态
     */
    Result<Integer> getPlayerProtectionStatus(Long appId, Long playerId);

    // 错误码定义
    int GET_PROTECTION_LIST_FAIL = 2490021;
    int GET_PROTECTION_LIST_PARAM_ERROR = 2490022;
    int CREATE_PROTECTION_FAIL = 2490023;
    int CREATE_PROTECTION_PARAM_ERROR = 2490024;
    int UPDATE_PROTECTION_FAIL = 2490025;
    int UPDATE_PROTECTION_PARAM_ERROR = 2490026;
    int DELETE_PROTECTION_FAIL = 2490027;
    int AGREE_PROTECTION_FAIL = 2490028;
    int REJECT_PROTECTION_FAIL = 2490029;
    int GET_PROTECTION_BY_ID_FAIL = 2490030;
}
