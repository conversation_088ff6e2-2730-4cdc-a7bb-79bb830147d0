package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneRoomDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetRoomDataList;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneRoomDataService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneRoomDataManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 线下专区厅数据服务实现
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class OfflineZoneRoomDataServiceProvider implements OfflineZoneRoomDataService {

    @Autowired
    private OfflineZoneRoomDataManager offlineZoneRoomDataManager;

    /**
     * 获取厅数据列表
     *
     * @param request 请求参数
     * @return 分页厅数据列表
     */
    @Override
    public Result<PageBean<OfflineZoneRoomDataBean>> getRoomDataList(@Valid RequestGetRoomDataList request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        
        try {
            // 参数校验
            if (request.getAppId() == null) {
                return RpcResult.fail("appId不能为空");
            }

            // 调用Manager层处理业务逻辑
            PageBean<OfflineZoneRoomDataBean> pageBean = offlineZoneRoomDataManager.getRoomDataList(request);

            LogContext.addResLog("response size={}, total={}", 
                    pageBean.getList() != null ? pageBean.getList().size() : 0, 
                    pageBean.getTotal());

            return RpcResult.success(pageBean);
            
        } catch (Exception e) {
            log.error("获取厅数据列表失败, request={}", JsonUtils.toJsonString(request), e);
            LogContext.addResLog("error={}", e.getMessage());
            return RpcResult.fail("获取厅数据列表失败");
        }
    }
}
