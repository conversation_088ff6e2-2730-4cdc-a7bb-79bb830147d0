package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneProtectionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestCreateProtection;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestUpdateProtection;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * 线下专区主播跳槽保护协议转换器
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface OfflineZoneProtectionConvert {

    OfflineZoneProtectionConvert INSTANCE = Mappers.getMapper(OfflineZoneProtectionConvert.class);

    /**
     * Entity 转换为 Bean
     *
     * @param entity 实体对象
     * @return Bean对象
     */
    @Mapping(target = "protectionStatusDesc", ignore = true)
    @Mapping(target = "isValid", ignore = true)
    OfflineZoneProtectionBean entityToBean(OfflineZoneProtection entity);

    /**
     * Entity 列表转换为 Bean 列表
     *
     * @param entities 实体列表
     * @return Bean列表
     */
    List<OfflineZoneProtectionBean> entityListToBeanList(List<OfflineZoneProtection> entities);

    /**
     * Entity 分页转换为 Bean 分页
     *
     * @param entityPage 实体分页
     * @return Bean分页
     */
    default PageBean<OfflineZoneProtectionBean> entityPageToBeanPage(PageBean<OfflineZoneProtection> entityPage) {
        if (entityPage == null) {
            return null;
        }
        
        List<OfflineZoneProtectionBean> beanList = entityListToBeanList(entityPage.getList());
        
        // 填充额外字段
        if (beanList != null) {
            Date now = new Date();
            beanList.forEach(bean -> {
                // 设置保护状态描述
                bean.setProtectionStatusDesc(getProtectionStatusDesc(bean));
                
                // 设置是否有效
                bean.setIsValid(isProtectionValid(bean, now));
            });
        }
        
        return PageBean.of(entityPage.getTotal(), beanList);
    }

    /**
     * 创建请求转换为 Entity
     *
     * @param request 创建请求
     * @return Entity对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "playerAgree", constant = "-1") // 默认未处理
    @Mapping(target = "archived", constant = "false") // 默认未归档
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "modifyTime", ignore = true)
    @Mapping(target = "agreementUpdateTime", ignore = true)
    @Mapping(target = "deployEnv", constant = "PRO") // 默认生产环境
    OfflineZoneProtection createRequestToEntity(RequestCreateProtection request);

    /**
     * 更新请求转换为 Entity
     *
     * @param request 更新请求
     * @param target  目标Entity
     * @return 更新后的Entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "appId", ignore = true)
    @Mapping(target = "familyId", ignore = true)
    @Mapping(target = "uploadUserId", ignore = true)
    @Mapping(target = "njId", ignore = true)
    @Mapping(target = "playerId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "modifyTime", ignore = true)
    @Mapping(target = "agreementUpdateTime", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    OfflineZoneProtection updateRequestToEntity(RequestUpdateProtection request, @MappingTarget OfflineZoneProtection target);

    /**
     * 获取保护状态描述
     *
     * @param bean 保护协议Bean
     * @return 状态描述
     */
    default String getProtectionStatusDesc(OfflineZoneProtectionBean bean) {
        if (bean == null) {
            return "未上传";
        }

        Date now = new Date();
        
        // 检查协议是否过期
        if (bean.getAgreementEndTime() != null && bean.getAgreementEndTime().before(now)) {
            return "上传过期";
        }

        // 检查协议是否生效
        if (bean.getAgreementStartTime() != null && bean.getAgreementStartTime().after(now)) {
            return "已上传";
        }

        // 协议已生效，检查主播同意状态
        if (bean.getPlayerAgree() != null) {
            switch (bean.getPlayerAgree()) {
                case 1:
                    return "主播同意";
                case 0:
                    return "主播拒绝";
                case -1:
                default:
                    return "已生效";
            }
        }

        return "已生效";
    }

    /**
     * 判断保护协议是否有效
     *
     * @param bean 保护协议Bean
     * @param now  当前时间
     * @return 是否有效
     */
    default Boolean isProtectionValid(OfflineZoneProtectionBean bean, Date now) {
        if (bean == null || bean.getAgreementStartTime() == null || bean.getAgreementEndTime() == null) {
            return false;
        }

        return !bean.getAgreementStartTime().after(now) && !bean.getAgreementEndTime().before(now);
    }
}
