package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneProtectionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线下专区-主播跳槽保护协议 Dao
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneProtectionDao {

    @Autowired
    private OfflineZoneProtectionMapper offlineZoneProtectionMapper;

    /**
     * 根据主播ID查询有效的保护协议
     *
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 保护协议
     */
    public OfflineZoneProtection getValidProtectionByPlayerId(Long appId, Long playerId) {
        OfflineZoneProtectionExample example = new OfflineZoneProtectionExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andPlayerIdEqualTo(playerId)
                .andAgreementStartTimeLessThanOrEqualTo(new Date())
                .andAgreementEndTimeGreaterThanOrEqualTo(new Date());
        example.setOrderByClause("agreement_update_time DESC LIMIT 1");

        List<OfflineZoneProtection> list = offlineZoneProtectionMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 根据主播ID查询最新的保护协议（包括过期的）
     *
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 保护协议
     */
    public OfflineZoneProtection getLatestProtectionByPlayerId(Long appId, Long playerId) {
        OfflineZoneProtectionExample example = new OfflineZoneProtectionExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andPlayerIdEqualTo(playerId);
        example.setOrderByClause("agreement_update_time DESC LIMIT 1");

        List<OfflineZoneProtection> list = offlineZoneProtectionMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 批量查询主播的保护协议映射
     *
     * @param appId     应用ID
     * @param playerIds 主播ID集合
     * @return 主播ID -> 保护协议的映射
     */
    public Map<Long, OfflineZoneProtection> getProtectionMapByPlayerIds(Long appId, Set<Long> playerIds) {
        if (CollectionUtils.isEmpty(playerIds)) {
            return Collections.emptyMap();
        }

        OfflineZoneProtectionExample example = new OfflineZoneProtectionExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andPlayerIdIn(new ArrayList<>(playerIds));
        example.setOrderByClause("agreement_update_time DESC");

        List<OfflineZoneProtection> list = offlineZoneProtectionMapper.selectByExample(example);

        // 按主播ID分组，取最新的协议
        return list.stream()
                .collect(Collectors.toMap(
                        OfflineZoneProtection::getPlayerId,
                        protection -> protection,
                        (existing, replacement) -> {
                            // 如果有多个协议，取更新时间最新的
                            if (replacement.getAgreementUpdateTime() != null && existing.getAgreementUpdateTime() != null) {
                                return replacement.getAgreementUpdateTime().after(existing.getAgreementUpdateTime()) 
                                        ? replacement : existing;
                            }
                            return replacement;
                        }
                ));
    }

    /**
     * 根据厅主ID查询保护协议列表
     *
     * @param appId 应用ID
     * @param njId  厅主ID
     * @return 保护协议列表
     */
    public List<OfflineZoneProtection> getProtectionsByNjId(Long appId, Long njId) {
        OfflineZoneProtectionExample example = new OfflineZoneProtectionExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andNjIdEqualTo(njId);
        example.setOrderByClause("agreement_update_time DESC");

        return offlineZoneProtectionMapper.selectByExample(example);
    }

    /**
     * 根据家族ID查询保护协议列表
     *
     * @param appId    应用ID
     * @param familyId 家族ID
     * @return 保护协议列表
     */
    public List<OfflineZoneProtection> getProtectionsByFamilyId(Long appId, Long familyId) {
        OfflineZoneProtectionExample example = new OfflineZoneProtectionExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId);
        example.setOrderByClause("agreement_update_time DESC");

        return offlineZoneProtectionMapper.selectByExample(example);
    }

    /**
     * 分页查询保护协议列表
     *
     * @param appId      应用ID
     * @param familyId   家族ID（可选）
     * @param njId       厅主ID（可选）
     * @param playerId   主播ID（可选）
     * @param pageNumber 页码
     * @param pageSize   页大小
     * @return 分页保护协议列表
     */
    public PageList<OfflineZoneProtection> getProtectionList(Long appId, Long familyId, Long njId, 
                                                           Long playerId, int pageNumber, int pageSize) {
        OfflineZoneProtectionExample example = new OfflineZoneProtectionExample();
        OfflineZoneProtectionExample.Criteria criteria = example.createCriteria();

        criteria.andAppIdEqualTo(appId);

        if (familyId != null) {
            criteria.andFamilyIdEqualTo(familyId);
        }

        if (njId != null) {
            criteria.andNjIdEqualTo(njId);
        }

        if (playerId != null) {
            criteria.andPlayerIdEqualTo(playerId);
        }

        example.setOrderByClause("agreement_update_time DESC");

        return offlineZoneProtectionMapper.pageByExample(example, pageNumber, pageSize);
    }

    /**
     * 插入保护协议
     *
     * @param protection 保护协议
     * @return 插入的记录数
     */
    public int insertProtection(OfflineZoneProtection protection) {
        protection.setCreateTime(new Date());
        protection.setModifyTime(new Date());
        return offlineZoneProtectionMapper.insertSelective(protection);
    }

    /**
     * 更新保护协议
     *
     * @param protection 保护协议
     * @return 更新的记录数
     */
    public int updateProtection(OfflineZoneProtection protection) {
        protection.setModifyTime(new Date());
        return offlineZoneProtectionMapper.updateByPrimaryKeySelective(protection);
    }

    /**
     * 根据ID查询保护协议
     *
     * @param id 协议ID
     * @return 保护协议
     */
    public OfflineZoneProtection getById(Long id) {
        return offlineZoneProtectionMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据ID删除保护协议
     *
     * @param id 协议ID
     * @return 删除的记录数
     */
    public int deleteById(Long id) {
        return offlineZoneProtectionMapper.deleteByPrimaryKey(id);
    }

    /**
     * 查询即将过期的保护协议（用于提醒）
     *
     * @param appId 应用ID
     * @param days  提前天数
     * @return 即将过期的保护协议列表
     */
    public List<OfflineZoneProtection> getExpiringProtections(Long appId, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, days);
        Date futureDate = calendar.getTime();

        OfflineZoneProtectionExample example = new OfflineZoneProtectionExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andAgreementEndTimeLessThanOrEqualTo(futureDate)
                .andAgreementEndTimeGreaterThanOrEqualTo(new Date());

        return offlineZoneProtectionMapper.selectByExample(example);
    }
}
