package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 厅数据列表查询DTO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GetRoomDataListParam {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 厅分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 排序字段
     */
    private String orderMetrics;

    /**
     * 排序类型
     */
    private String orderType;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页大小
     */
    private Integer pageSize;
}
