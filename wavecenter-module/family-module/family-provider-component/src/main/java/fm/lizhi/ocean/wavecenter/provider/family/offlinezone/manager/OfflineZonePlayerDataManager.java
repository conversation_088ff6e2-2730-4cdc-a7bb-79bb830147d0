package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZonePlayerDataBean;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZonePlayerDataConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataPlayerWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneProtectionDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetPlayerDataListParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线下专区主播数据管理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZonePlayerDataManager {

    @Autowired
    private OfflineZoneDataPlayerWeekDao offlineZoneDataPlayerWeekDao;

    @Autowired
    private OfflineZoneProtectionDao offlineZoneProtectionDao;

    @Autowired
    private UserCommonService userService;

    /**
     * 获取主播数据列表
     *
     * @param queryDTO 查询DTO
     * @return 分页主播数据列表
     */
    public PageBean<OfflineZonePlayerDataBean> getPlayerDataList(GetPlayerDataListParam queryDTO) {
        // 查询数据
        PageList<OfflineZoneDataPlayerWeek> pageList = offlineZoneDataPlayerWeekDao.getPlayerDataList(queryDTO);

        // 转换数据
        List<OfflineZonePlayerDataBean> list = OfflineZonePlayerDataConvert.INSTANCE.entityListToBeanList(pageList);

        PageBean<OfflineZonePlayerDataBean> pageBean = PageBean.of(pageList.getTotal(), list);

        // 填充用户信息
        fillUserInfo(queryDTO.getAppId(), pageBean);

        // 填充保护状态信息
        fillProtectionInfo(queryDTO.getAppId(), pageBean);

        // 根据保护状态过滤
        filterByProtection(queryDTO, pageBean);

        return pageBean;
    }

    /**
     * 填充用户信息
     *
     * @param appId 应用ID
     * @param pageBean 分页数据
     */
    private void fillUserInfo(Integer appId, PageBean<OfflineZonePlayerDataBean> pageBean) {
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return;
        }

        // 收集所有用户ID（主播ID和厅主ID）
        Set<Long> userIds = new HashSet<>();
        pageBean.getList().forEach(playerDataBean -> {
            if (playerDataBean.getUserId() != null) {
                userIds.add(playerDataBean.getUserId());
            }
            if (playerDataBean.getNjId() != null) {
                userIds.add(playerDataBean.getNjId());
            }
        });

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        try {
            // 批量获取用户信息
            Map<Long, UserBean> userMap = getUserMap(appId, userIds);

            // 填充用户信息
            pageBean.getList().forEach(playerDataBean -> {
                // 填充主播信息
                UserBean userBean = userMap.get(playerDataBean.getUserId());
                if (userBean != null) {
                    playerDataBean.setUserInfo(userBean);
                }

                // 填充厅主信息
                UserBean njBean = userMap.get(playerDataBean.getNjId());
                if (njBean != null) {
                    playerDataBean.setNjInfo(njBean);
                }
            });
        } catch (Exception e) {
            log.warn("获取用户信息失败, appId={}, userIds={}", appId, userIds, e);
        }
    }

    /**
     * 填充保护状态信息
     *
     * @param appId 应用ID
     * @param pageBean 分页数据
     */
    private void fillProtectionInfo(Integer appId, PageBean<OfflineZonePlayerDataBean> pageBean) {
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return;
        }

        // 收集所有主播ID
        Set<Long> playerIds = pageBean.getList().stream()
                .map(OfflineZonePlayerDataBean::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(playerIds)) {
            return;
        }

        try {
            // 批量获取保护信息
            Map<Long, OfflineZoneProtection> protectionMap = offlineZoneProtectionDao.getProtectionMapByPlayerIds(appId.longValue(), playerIds);

            // 填充保护状态
            pageBean.getList().forEach(playerDataBean -> {
                OfflineZoneProtection protection = protectionMap.get(playerDataBean.getUserId());
                if (protection != null) {
                    playerDataBean.setProtection(true);
                    playerDataBean.setProtectionStatus(getProtectionStatusDesc(protection));
                } else {
                    playerDataBean.setProtection(false);
                    playerDataBean.setProtectionStatus("未上传");
                }
            });
        } catch (Exception e) {
            log.warn("获取保护状态信息失败, appId={}, playerIds={}", appId, playerIds, e);
        }
    }

    /**
     * 根据保护状态过滤
     *
     * @param queryDTO 查询参数
     * @param pageBean 分页数据
     */
    private void filterByProtection(GetPlayerDataListParam queryDTO, PageBean<OfflineZonePlayerDataBean> pageBean) {
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return;
        }

        List<OfflineZonePlayerDataBean> filteredList = pageBean.getList();

        // 按保护状态过滤
        if (StringUtils.hasText(queryDTO.getProtection())) {
            boolean isProtected = "true".equalsIgnoreCase(queryDTO.getProtection());
            filteredList = filteredList.stream()
                    .filter(bean -> Objects.equals(bean.getProtection(), isProtected))
                    .collect(Collectors.toList());
        }

        // 按具体保护状态过滤
        if (StringUtils.hasText(queryDTO.getProtectionStatus())) {
            String targetStatus = getProtectionStatusDescByCode(queryDTO.getProtectionStatus());
            filteredList = filteredList.stream()
                    .filter(bean -> Objects.equals(bean.getProtectionStatus(), targetStatus))
                    .collect(Collectors.toList());
        }

        pageBean.setList(filteredList);
        pageBean.setTotal((long) filteredList.size());
    }

    /**
     * 获取保护状态描述
     *
     * @param protection 保护协议
     * @return 状态描述
     */
    private String getProtectionStatusDesc(OfflineZoneProtection protection) {
        if (protection == null) {
            return "未上传";
        }

        Date now = new Date();
        
        // 检查协议是否过期
        if (protection.getAgreementEndTime() != null && protection.getAgreementEndTime().before(now)) {
            return "上传过期";
        }

        // 检查协议是否生效
        if (protection.getAgreementStartTime() != null && protection.getAgreementStartTime().after(now)) {
            return "已上传";
        }

        // 协议已生效，检查主播同意状态
        if (protection.getPlayerAgree() != null) {
            switch (protection.getPlayerAgree()) {
                case 1:
                    return "主播同意";
                case 0:
                    return "主播拒绝";
                case -1:
                default:
                    return "已生效";
            }
        }

        return "已生效";
    }

    /**
     * 根据状态码获取状态描述
     *
     * @param statusCode 状态码
     * @return 状态描述
     */
    private String getProtectionStatusDescByCode(String statusCode) {
        switch (statusCode) {
            case "0":
                return "未上传";
            case "1":
                return "已上传";
            case "2":
                return "已生效";
            case "3":
                return "主播同意";
            case "4":
                return "主播拒绝";
            case "5":
                return "上传过期";
            default:
                return "未上传";
        }
    }

    /**
     * 批量获取用户信息
     *
     * @param appId 应用ID
     * @param userIds 用户ID集合
     * @return 用户信息映射
     */
    private Map<Long, UserBean> getUserMap(Integer appId, Set<Long> userIds) {
        Result<List<UserBean>> result = userService.getUserByIds(appId, new ArrayList<>(userIds));
        if (RpcResult.isSuccess(result)) {
            return result.target().stream()
                    .collect(Collectors.toMap(UserBean::getId, userBean -> userBean, (a, b) -> a));
        }
        return Collections.emptyMap();
    }
}
