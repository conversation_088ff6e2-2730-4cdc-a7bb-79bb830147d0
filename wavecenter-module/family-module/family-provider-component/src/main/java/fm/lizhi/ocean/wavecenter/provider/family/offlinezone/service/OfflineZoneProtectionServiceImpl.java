package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneProtectionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestCreateProtection;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetProtectionList;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestUpdateProtection;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneProtectionService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneProtectionConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneProtectionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import java.util.Date;

/**
 * 线下专区主播跳槽保护协议服务实现
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class OfflineZoneProtectionServiceImpl implements OfflineZoneProtectionService {

    @Autowired
    private OfflineZoneProtectionManager offlineZoneProtectionManager;

    @Override
    public Result<PageBean<OfflineZoneProtectionBean>> getProtectionList(@Valid RequestGetProtectionList request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));

        try {
            if (request.getAppId() == null) {
                return RpcResult.fail(GET_PROTECTION_LIST_PARAM_ERROR, "appId不能为空");
            }

            int pageNumber = request.getPageNo() != null && request.getPageNo() > 0 ? request.getPageNo() : 1;
            int pageSize = request.getPageSize() != null && request.getPageSize() > 0 ? request.getPageSize() : 20;

            PageBean<OfflineZoneProtection> pageBean = offlineZoneProtectionManager.getProtectionList(
                    request.getAppId(), request.getFamilyId(), request.getNjId(), 
                    request.getPlayerId(), pageNumber, pageSize);

            PageBean<OfflineZoneProtectionBean> result = OfflineZoneProtectionConvert.INSTANCE.entityPageToBeanPage(pageBean);

            LogContext.addResLog("response size={}, total={}", 
                    result.getList() != null ? result.getList().size() : 0, 
                    result.getTotal());

            return RpcResult.success(result);

        } catch (Exception e) {
            log.error("获取保护协议列表失败, request={}", JsonUtils.toJsonString(request), e);
            return RpcResult.fail(GET_PROTECTION_LIST_FAIL, "获取保护协议列表失败");
        }
    }

    @Override
    public Result<Boolean> createProtection(@Valid RequestCreateProtection request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));

        try {
            if (request.getAppId() == null || request.getFamilyId() == null || 
                request.getNjId() == null || request.getPlayerId() == null) {
                return RpcResult.fail(CREATE_PROTECTION_PARAM_ERROR, "必要参数不能为空");
            }

            OfflineZoneProtection protection = OfflineZoneProtectionConvert.INSTANCE.createRequestToEntity(request);
            boolean success = offlineZoneProtectionManager.createProtection(protection);

            LogContext.addResLog("response={}", success);
            return RpcResult.success(success);

        } catch (Exception e) {
            log.error("创建保护协议失败, request={}", JsonUtils.toJsonString(request), e);
            return RpcResult.fail(CREATE_PROTECTION_FAIL, "创建保护协议失败");
        }
    }

    @Override
    public Result<Boolean> updateProtection(@Valid RequestUpdateProtection request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));

        try {
            if (request.getId() == null) {
                return RpcResult.fail(UPDATE_PROTECTION_PARAM_ERROR, "id不能为空");
            }

            // 先查询现有协议
            OfflineZoneProtection existingProtection = offlineZoneProtectionManager.getProtectionById(request.getId());
            if (existingProtection == null) {
                return RpcResult.fail(UPDATE_PROTECTION_FAIL, "保护协议不存在");
            }

            // 更新字段
            OfflineZoneProtection protection = OfflineZoneProtectionConvert.INSTANCE.updateRequestToEntity(request, existingProtection);
            boolean success = offlineZoneProtectionManager.updateProtection(protection);

            LogContext.addResLog("response={}", success);
            return RpcResult.success(success);

        } catch (Exception e) {
            log.error("更新保护协议失败, request={}", JsonUtils.toJsonString(request), e);
            return RpcResult.fail(UPDATE_PROTECTION_FAIL, "更新保护协议失败");
        }
    }

    @Override
    public Result<Boolean> agreeProtection(Long appId, Long playerId) {
        LogContext.addReqLog("appId={}, playerId={}", appId, playerId);

        try {
            if (appId == null || playerId == null) {
                return RpcResult.fail(AGREE_PROTECTION_FAIL, "appId和playerId不能为空");
            }

            boolean success = offlineZoneProtectionManager.agreeProtection(appId, playerId);

            LogContext.addResLog("response={}", success);
            return RpcResult.success(success);

        } catch (Exception e) {
            log.error("主播同意保护协议失败, appId={}, playerId={}", appId, playerId, e);
            return RpcResult.fail(AGREE_PROTECTION_FAIL, "主播同意保护协议失败");
        }
    }

    @Override
    public Result<Boolean> rejectProtection(Long appId, Long playerId) {
        LogContext.addReqLog("appId={}, playerId={}", appId, playerId);

        try {
            if (appId == null || playerId == null) {
                return RpcResult.fail(REJECT_PROTECTION_FAIL, "appId和playerId不能为空");
            }

            boolean success = offlineZoneProtectionManager.rejectProtection(appId, playerId);

            LogContext.addResLog("response={}", success);
            return RpcResult.success(success);

        } catch (Exception e) {
            log.error("主播拒绝保护协议失败, appId={}, playerId={}", appId, playerId, e);
            return RpcResult.fail(REJECT_PROTECTION_FAIL, "主播拒绝保护协议失败");
        }
    }

    @Override
    public Result<Boolean> deleteProtection(Long id) {
        LogContext.addReqLog("id={}", id);

        try {
            if (id == null) {
                return RpcResult.fail(DELETE_PROTECTION_FAIL, "id不能为空");
            }

            boolean success = offlineZoneProtectionManager.deleteProtection(id);

            LogContext.addResLog("response={}", success);
            return RpcResult.success(success);

        } catch (Exception e) {
            log.error("删除保护协议失败, id={}", id, e);
            return RpcResult.fail(DELETE_PROTECTION_FAIL, "删除保护协议失败");
        }
    }

    @Override
    public Result<OfflineZoneProtectionBean> getProtectionById(Long id) {
        LogContext.addReqLog("id={}", id);

        try {
            if (id == null) {
                return RpcResult.fail(GET_PROTECTION_BY_ID_FAIL, "id不能为空");
            }

            OfflineZoneProtection protection = offlineZoneProtectionManager.getProtectionById(id);
            OfflineZoneProtectionBean result = OfflineZoneProtectionConvert.INSTANCE.entityToBean(protection);

            LogContext.addResLog("response={}", result != null);
            return RpcResult.success(result);

        } catch (Exception e) {
            log.error("根据ID查询保护协议失败, id={}", id, e);
            return RpcResult.fail(GET_PROTECTION_BY_ID_FAIL, "根据ID查询保护协议失败");
        }
    }

    @Override
    public Result<Boolean> isPlayerProtected(Long appId, Long playerId) {
        try {
            if (appId == null || playerId == null) {
                return RpcResult.success(false);
            }

            boolean isProtected = offlineZoneProtectionManager.isPlayerProtected(appId, playerId);
            return RpcResult.success(isProtected);

        } catch (Exception e) {
            log.error("检查主播保护状态失败, appId={}, playerId={}", appId, playerId, e);
            return RpcResult.success(false);
        }
    }

    @Override
    public Result<Integer> getPlayerProtectionStatus(Long appId, Long playerId) {
        try {
            if (appId == null || playerId == null) {
                return RpcResult.success(0); // 未上传
            }

            int status = offlineZoneProtectionManager.getPlayerProtectionStatus(appId, playerId);
            return RpcResult.success(status);

        } catch (Exception e) {
            log.error("获取主播保护状态失败, appId={}, playerId={}", appId, playerId, e);
            return RpcResult.success(0); // 未上传
        }
    }
}
