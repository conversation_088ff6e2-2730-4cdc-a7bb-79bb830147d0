package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneProtectionDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 线下专区主播跳槽保护协议管理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneProtectionManager {

    @Autowired
    private OfflineZoneProtectionDao offlineZoneProtectionDao;

    /**
     * 根据主播ID查询有效的保护协议
     *
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 保护协议
     */
    public OfflineZoneProtection getValidProtectionByPlayerId(Long appId, Long playerId) {
        return offlineZoneProtectionDao.getValidProtectionByPlayerId(appId, playerId);
    }

    /**
     * 根据主播ID查询最新的保护协议
     *
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 保护协议
     */
    public OfflineZoneProtection getLatestProtectionByPlayerId(Long appId, Long playerId) {
        return offlineZoneProtectionDao.getLatestProtectionByPlayerId(appId, playerId);
    }

    /**
     * 批量查询主播的保护协议映射
     *
     * @param appId     应用ID
     * @param playerIds 主播ID集合
     * @return 主播ID -> 保护协议的映射
     */
    public Map<Long, OfflineZoneProtection> getProtectionMapByPlayerIds(Long appId, Set<Long> playerIds) {
        return offlineZoneProtectionDao.getProtectionMapByPlayerIds(appId, playerIds);
    }

    /**
     * 分页查询保护协议列表
     *
     * @param appId      应用ID
     * @param familyId   家族ID（可选）
     * @param njId       厅主ID（可选）
     * @param playerId   主播ID（可选）
     * @param pageNumber 页码
     * @param pageSize   页大小
     * @return 分页保护协议列表
     */
    public PageBean<OfflineZoneProtection> getProtectionList(Long appId, Long familyId, Long njId, 
                                                           Long playerId, int pageNumber, int pageSize) {
        PageList<OfflineZoneProtection> pageList = offlineZoneProtectionDao.getProtectionList(
                appId, familyId, njId, playerId, pageNumber, pageSize);
        
        return PageBean.of(pageList.getTotal(), pageList);
    }

    /**
     * 创建保护协议
     *
     * @param protection 保护协议
     * @return 是否创建成功
     */
    public boolean createProtection(OfflineZoneProtection protection) {
        try {
            // 设置创建时间和更新时间
            Date now = new Date();
            protection.setCreateTime(now);
            protection.setModifyTime(now);
            protection.setAgreementUpdateTime(now);

            int result = offlineZoneProtectionDao.insertProtection(protection);
            return result > 0;
        } catch (Exception e) {
            log.error("创建保护协议失败, protection={}", protection, e);
            return false;
        }
    }

    /**
     * 更新保护协议
     *
     * @param protection 保护协议
     * @return 是否更新成功
     */
    public boolean updateProtection(OfflineZoneProtection protection) {
        try {
            // 设置更新时间
            protection.setModifyTime(new Date());
            protection.setAgreementUpdateTime(new Date());

            int result = offlineZoneProtectionDao.updateProtection(protection);
            return result > 0;
        } catch (Exception e) {
            log.error("更新保护协议失败, protection={}", protection, e);
            return false;
        }
    }

    /**
     * 主播同意保护协议
     *
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 是否操作成功
     */
    public boolean agreeProtection(Long appId, Long playerId) {
        try {
            OfflineZoneProtection protection = offlineZoneProtectionDao.getLatestProtectionByPlayerId(appId, playerId);
            if (protection == null) {
                log.warn("未找到主播的保护协议, appId={}, playerId={}", appId, playerId);
                return false;
            }

            protection.setPlayerAgree(1); // 1表示同意
            protection.setModifyTime(new Date());

            int result = offlineZoneProtectionDao.updateProtection(protection);
            return result > 0;
        } catch (Exception e) {
            log.error("主播同意保护协议失败, appId={}, playerId={}", appId, playerId, e);
            return false;
        }
    }

    /**
     * 主播拒绝保护协议
     *
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 是否操作成功
     */
    public boolean rejectProtection(Long appId, Long playerId) {
        try {
            OfflineZoneProtection protection = offlineZoneProtectionDao.getLatestProtectionByPlayerId(appId, playerId);
            if (protection == null) {
                log.warn("未找到主播的保护协议, appId={}, playerId={}", appId, playerId);
                return false;
            }

            protection.setPlayerAgree(0); // 0表示不同意
            protection.setModifyTime(new Date());

            int result = offlineZoneProtectionDao.updateProtection(protection);
            return result > 0;
        } catch (Exception e) {
            log.error("主播拒绝保护协议失败, appId={}, playerId={}", appId, playerId, e);
            return false;
        }
    }

    /**
     * 删除保护协议
     *
     * @param id 协议ID
     * @return 是否删除成功
     */
    public boolean deleteProtection(Long id) {
        try {
            int result = offlineZoneProtectionDao.deleteById(id);
            return result > 0;
        } catch (Exception e) {
            log.error("删除保护协议失败, id={}", id, e);
            return false;
        }
    }

    /**
     * 根据ID查询保护协议
     *
     * @param id 协议ID
     * @return 保护协议
     */
    public OfflineZoneProtection getProtectionById(Long id) {
        return offlineZoneProtectionDao.getById(id);
    }

    /**
     * 查询即将过期的保护协议
     *
     * @param appId 应用ID
     * @param days  提前天数
     * @return 即将过期的保护协议列表
     */
    public List<OfflineZoneProtection> getExpiringProtections(Long appId, int days) {
        return offlineZoneProtectionDao.getExpiringProtections(appId, days);
    }

    /**
     * 检查主播是否受保护
     *
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 是否受保护
     */
    public boolean isPlayerProtected(Long appId, Long playerId) {
        OfflineZoneProtection protection = offlineZoneProtectionDao.getValidProtectionByPlayerId(appId, playerId);
        return protection != null && protection.getPlayerAgree() != null && protection.getPlayerAgree() == 1;
    }

    /**
     * 获取主播保护状态
     *
     * @param appId    应用ID
     * @param playerId 主播ID
     * @return 保护状态：0-未上传，1-已上传，2-已生效，3-主播同意，4-主播拒绝，5-上传过期
     */
    public int getPlayerProtectionStatus(Long appId, Long playerId) {
        OfflineZoneProtection protection = offlineZoneProtectionDao.getLatestProtectionByPlayerId(appId, playerId);
        
        if (protection == null) {
            return 0; // 未上传
        }

        Date now = new Date();
        
        // 检查协议是否过期
        if (protection.getAgreementEndTime() != null && protection.getAgreementEndTime().before(now)) {
            return 5; // 上传过期
        }

        // 检查协议是否生效
        if (protection.getAgreementStartTime() != null && protection.getAgreementStartTime().after(now)) {
            return 1; // 已上传
        }

        // 协议已生效，检查主播同意状态
        if (protection.getPlayerAgree() != null) {
            switch (protection.getPlayerAgree()) {
                case 1:
                    return 3; // 主播同意
                case 0:
                    return 4; // 主播拒绝
                case -1:
                default:
                    return 2; // 已生效
            }
        }

        return 2; // 已生效
    }
}
