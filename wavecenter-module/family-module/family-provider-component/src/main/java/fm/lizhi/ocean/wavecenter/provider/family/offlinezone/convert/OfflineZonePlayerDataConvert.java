package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZonePlayerDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetPlayerDataList;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetPlayerDataListParam;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 线下专区主播数据转换器
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface OfflineZonePlayerDataConvert {

    OfflineZonePlayerDataConvert INSTANCE = Mappers.getMapper(OfflineZonePlayerDataConvert.class);

    /**
     * Request 转换为 DTO
     *
     * @param request 请求对象
     * @return DTO对象
     */
    GetPlayerDataListParam requestToDTO(RequestGetPlayerDataList request);

    /**
     * Entity 转换为 Bean
     *
     * @param entity 实体对象
     * @return Bean对象
     */
    @Mapping(target = "userInfo", ignore = true)
    @Mapping(target = "njInfo", ignore = true)
    @Mapping(target = "protection", ignore = true)
    @Mapping(target = "protectionStatus", ignore = true)
    OfflineZonePlayerDataBean entityToBean(OfflineZoneDataPlayerWeek entity);

    /**
     * Entity 列表转换为 Bean 列表
     *
     * @param entities 实体列表
     * @return Bean列表
     */
    List<OfflineZonePlayerDataBean> entityListToBeanList(List<OfflineZoneDataPlayerWeek> entities);
}
