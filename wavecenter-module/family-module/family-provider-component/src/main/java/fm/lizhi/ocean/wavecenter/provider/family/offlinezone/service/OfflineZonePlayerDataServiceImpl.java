package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZonePlayerDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetPlayerDataList;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZonePlayerDataService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZonePlayerDataConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetPlayerDataListParam;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZonePlayerDataManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;

/**
 * 线下专区主播数据服务实现
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class OfflineZonePlayerDataServiceImpl implements OfflineZonePlayerDataService {

    @Autowired
    private OfflineZonePlayerDataManager offlineZonePlayerDataManager;

    /**
     * 获取主播数据列表
     *
     * @param request 请求参数
     * @return 分页主播数据列表
     */
    @Override
    public Result<PageBean<OfflineZonePlayerDataBean>> getPlayerDataList(@Valid RequestGetPlayerDataList request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));

        try {
            // 参数校验
            if (request.getAppId() == null) {
                return RpcResult.fail(OfflineZonePlayerDataService.GET_PLAYER_DATA_LIST_PARAM_ERROR, "appId不能为空");
            }

            // 转换请求参数
            GetPlayerDataListParam param = OfflineZonePlayerDataConvert.INSTANCE.requestToDTO(request);

            PageBean<OfflineZonePlayerDataBean> pageBean = offlineZonePlayerDataManager.getPlayerDataList(param);

            LogContext.addResLog("response size={}, total={}", 
                    pageBean.getList() != null ? pageBean.getList().size() : 0, 
                    pageBean.getTotal());

            return RpcResult.success(pageBean);

        } catch (Exception e) {
            log.error("获取主播数据列表失败, request={}", JsonUtils.toJsonString(request), e);
            return RpcResult.fail(OfflineZonePlayerDataService.GET_PLAYER_DATA_LIST_FAIL, "获取主播数据列表失败");
        }
    }
}
