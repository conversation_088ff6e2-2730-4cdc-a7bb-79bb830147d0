package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 主播数据列表查询DTO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GetPlayerDataListParam {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 签约主播ID
     */
    private String playerId;

    /**
     * 签约主播名称
     */
    private String playerName;

    /**
     * 主播分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 签约厅ID
     */
    private String njId;

    /**
     * 家族ID
     */
    private String familyId;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序类型：ASC/DESC
     */
    private String orderType;

    /**
     * 排序指标
     */
    private String orderMetrics;

    /**
     * 是否受跳槽保护：true/false
     */
    private String protection;

    /**
     * 保护状态：0 未上传，1 已上传，2 已生效，3 主播同意，4 主播拒绝，5 上传过期
     */
    private String protectionStatus;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页大小
     */
    private Integer pageSize;
}
