package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneDataPlayerWeekMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetPlayerDataListParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 线下专区-主播明细-周 Dao
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneDataPlayerWeekDao {

    @Autowired
    private OfflineZoneDataPlayerWeekMapper offlineZoneDataPlayerWeekMapper;

    /**
     * 根据条件查询主播数据列表（支持分页和排序）
     *
     * @param queryDTO 查询DTO
     * @return 分页主播数据列表
     */
    public PageList<OfflineZoneDataPlayerWeek> getPlayerDataList(GetPlayerDataListParam queryDTO) {
        // 参数校验和默认值设置
        int pageNumber = queryDTO.getPageNo() != null && queryDTO.getPageNo() > 0 ? queryDTO.getPageNo() : 1;
        int pageSize = queryDTO.getPageSize() != null && queryDTO.getPageSize() > 0 ? queryDTO.getPageSize() : 20;

        // 限制页大小
        if (pageSize > 100) {
            pageSize = 100;
        }

        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        OfflineZoneDataPlayerWeekExample.Criteria criteria = example.createCriteria();

        // 必须条件
        criteria.andAppIdEqualTo(queryDTO.getAppId());

        // 可选条件
        if (StringUtils.hasText(queryDTO.getPlayerId())) {
            try {
                Long playerId = Long.valueOf(queryDTO.getPlayerId());
                criteria.andUserIdEqualTo(playerId);
            } catch (NumberFormatException e) {
                log.warn("playerId格式错误: {}", queryDTO.getPlayerId());
            }
        }

        if (StringUtils.hasText(queryDTO.getPlayerName())) {
            criteria.andUserNameLike("%" + queryDTO.getPlayerName() + "%");
        }

        if (queryDTO.getCategory() != null) {
            criteria.andCategoryEqualTo(queryDTO.getCategory());
        }

        if (StringUtils.hasText(queryDTO.getProvince())) {
            criteria.andProvinceEqualTo(queryDTO.getProvince());
        }

        if (StringUtils.hasText(queryDTO.getCity())) {
            criteria.andCityEqualTo(queryDTO.getCity());
        }

        if (StringUtils.hasText(queryDTO.getNjId())) {
            try {
                Long njId = Long.valueOf(queryDTO.getNjId());
                criteria.andNjIdEqualTo(njId);
            } catch (NumberFormatException e) {
                log.warn("njId格式错误: {}", queryDTO.getNjId());
            }
        }

        if (StringUtils.hasText(queryDTO.getFamilyId())) {
            try {
                Long familyId = Long.valueOf(queryDTO.getFamilyId());
                criteria.andFamilyIdEqualTo(familyId);
            } catch (NumberFormatException e) {
                log.warn("familyId格式错误: {}", queryDTO.getFamilyId());
            }
        }

        // 日期范围条件
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            Date start = DateUtil.formatStrToDate(queryDTO.getStartDate(), DateUtil.date_2);
            criteria.andStartWeekDateGreaterThanOrEqualTo(start);
        }

        if (StringUtils.hasText(queryDTO.getEndDate())) {
            Date end = DateUtil.formatStrToDate(queryDTO.getEndDate(), DateUtil.date_2);
            criteria.andEndWeekDateLessThanOrEqualTo(end);
        }

        // 排序条件
        String orderByClause = buildOrderByClause(queryDTO.getOrderMetrics(), queryDTO.getOrderType());
        if (StringUtils.hasText(orderByClause)) {
            example.setOrderByClause(orderByClause);
        } else {
            // 默认排序
            example.setOrderByClause("start_week_date DESC");
        }

        return offlineZoneDataPlayerWeekMapper.pageByExample(example, pageNumber, pageSize);
    }

    /**
     * 构建排序子句
     *
     * @param orderMetrics 排序字段
     * @param orderType    排序类型
     * @return 排序子句
     */
    private String buildOrderByClause(String orderMetrics, String orderType) {
        if (!StringUtils.hasText(orderMetrics)) {
            return null;
        }

        // 排序类型，默认为 DESC
        String order = "ASC".equalsIgnoreCase(orderType) ? "ASC" : "DESC";

        // 支持的排序字段映射
        String columnName;
        switch (orderMetrics) {
            case "income":
                columnName = "income";
                break;
            case "beginSignTime":
                columnName = "begin_sign_time";
                break;
            case "startWeekDate":
                columnName = "start_week_date";
                break;
            case "userName":
                columnName = "user_name";
                break;
            case "createTime":
                columnName = "create_time";
                break;
            default:
                log.warn("不支持的排序字段: {}", orderMetrics);
                return null;
        }

        return columnName + " " + order;
    }

    /**
     * 根据主播ID和时间范围查询主播数据
     *
     * @param appId         应用ID
     * @param userId        主播ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 主播周数据
     */
    public OfflineZoneDataPlayerWeek getByUserIdAndWeek(Integer appId, Long userId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andUserIdEqualTo(userId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate);

        List<OfflineZoneDataPlayerWeek> list = offlineZoneDataPlayerWeekMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 根据家族ID查询主播数据
     *
     * @param appId         应用ID
     * @param familyId      家族ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 主播周数据列表
     */
    public List<OfflineZoneDataPlayerWeek> getByFamilyIdAndWeek(Integer appId, Long familyId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate);
        
        return offlineZoneDataPlayerWeekMapper.selectByExample(example);
    }

    /**
     * 根据厅ID查询主播数据
     *
     * @param appId         应用ID
     * @param njId          厅主ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 主播周数据列表
     */
    public List<OfflineZoneDataPlayerWeek> getByNjIdAndWeek(Integer appId, Long njId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andNjIdEqualTo(njId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate);
        
        return offlineZoneDataPlayerWeekMapper.selectByExample(example);
    }

    /**
     * 根据家族ID查询线下主播数据
     *
     * @param appId         应用ID
     * @param familyId      家族ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 线下主播周数据列表
     */
    public List<OfflineZoneDataPlayerWeek> getOfflinePlayersByFamilyIdAndWeek(Integer appId, Long familyId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate)
                .andCategoryEqualTo(0); // 0表示线下主播
        
        return offlineZoneDataPlayerWeekMapper.selectByExample(example);
    }

    /**
     * 根据厅ID查询线下主播数据
     *
     * @param appId         应用ID
     * @param njId          厅主ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 线下主播周数据列表
     */
    public List<OfflineZoneDataPlayerWeek> getOfflinePlayersByNjIdAndWeek(Integer appId, Long njId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andNjIdEqualTo(njId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate)
                .andCategoryEqualTo(0); // 0表示线下主播
        
        return offlineZoneDataPlayerWeekMapper.selectByExample(example);
    }

    /**
     * 根据主播ID查询最新的主播数据
     *
     * @param appId  应用ID
     * @param userId 主播ID
     * @return 最新的主播周数据
     */
    public OfflineZoneDataPlayerWeek getLatestByUserId(Integer appId, Long userId) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andUserIdEqualTo(userId);
        example.setOrderByClause("start_week_date DESC LIMIT 1");
        
        List<OfflineZoneDataPlayerWeek> list = offlineZoneDataPlayerWeekMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }
}
