package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneDataPlayerWeekMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 线下专区-主播明细-周 Dao
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneDataPlayerWeekDao {

    @Autowired
    private OfflineZoneDataPlayerWeekMapper offlineZoneDataPlayerWeekMapper;

    /**
     * 根据家族ID查询主播数据
     *
     * @param appId         应用ID
     * @param familyId      家族ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 主播周数据列表
     */
    public List<OfflineZoneDataPlayerWeek> getByFamilyIdAndWeek(Integer appId, Long familyId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate);
        
        return offlineZoneDataPlayerWeekMapper.selectByExample(example);
    }

    /**
     * 根据厅ID查询主播数据
     *
     * @param appId         应用ID
     * @param njId          厅主ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 主播周数据列表
     */
    public List<OfflineZoneDataPlayerWeek> getByNjIdAndWeek(Integer appId, Long njId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andNjIdEqualTo(njId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate);
        
        return offlineZoneDataPlayerWeekMapper.selectByExample(example);
    }

    /**
     * 根据家族ID查询线下主播数据
     *
     * @param appId         应用ID
     * @param familyId      家族ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 线下主播周数据列表
     */
    public List<OfflineZoneDataPlayerWeek> getOfflinePlayersByFamilyIdAndWeek(Integer appId, Long familyId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate)
                .andCategoryEqualTo(0); // 0表示线下主播
        
        return offlineZoneDataPlayerWeekMapper.selectByExample(example);
    }

    /**
     * 根据厅ID查询线下主播数据
     *
     * @param appId         应用ID
     * @param njId          厅主ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 线下主播周数据列表
     */
    public List<OfflineZoneDataPlayerWeek> getOfflinePlayersByNjIdAndWeek(Integer appId, Long njId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andNjIdEqualTo(njId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate)
                .andCategoryEqualTo(0); // 0表示线下主播
        
        return offlineZoneDataPlayerWeekMapper.selectByExample(example);
    }

    /**
     * 根据主播ID查询最新的主播数据
     *
     * @param appId  应用ID
     * @param userId 主播ID
     * @return 最新的主播周数据
     */
    public OfflineZoneDataPlayerWeek getLatestByUserId(Integer appId, Long userId) {
        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andUserIdEqualTo(userId);
        example.setOrderByClause("start_week_date DESC LIMIT 1");
        
        List<OfflineZoneDataPlayerWeek> list = offlineZoneDataPlayerWeekMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }
}
